# 剪切板功能改进文档

## 概述

本次改进主要针对 `electron/helpers/clipboard.js` 中的 `copyFilePathMacOS` 和 `copyFilePathWindows` 函数，提高了它们的兼容性和可靠性。

## 改进内容

### macOS 平台改进 (`copyFilePathMacOS`)

#### 1. XML 转义处理
- **问题**: 原实现直接将路径插入 plist XML 中，如果路径包含特殊字符（`&`, `<`, `>`, `"`, `'`）会导致 XML 格式错误
- **解决方案**: 添加 `escapeXml()` 函数，正确转义所有 XML 特殊字符
- **示例**: 路径 `"/Users/<USER>/file&data.txt"` 会被转义为 `"/Users/<USER>/file&amp;data.txt"`

#### 2. 路径验证
- **功能**: 添加 `validateMacOSPath()` 函数验证路径格式
- **检查项目**:
  - 路径必须是字符串类型
  - 必须是绝对路径（以 `/` 开头）
  - 不能包含 null 字符（`\0`）
  - 路径长度不超过 1024 字符

#### 3. 路径规范化
- **功能**: 使用 `fs.realpathSync()` 解析符号链接，获取真实路径
- **容错**: 如果无法解析真实路径，使用原路径并记录警告

#### 4. 多格式支持
- **主格式**: `NSFilenamesPboardType` (plist 格式)
- **备选格式1**: `public.file-url` (文件 URL 格式)
- **备选格式2**: 纯文本格式
- **优势**: 如果主格式失败，自动尝试备选格式，提高兼容性

### Windows 平台改进 (`copyFilePathWindows`)

#### 1. 路径规范化
- **功能**: 改进 `normalizeWindowsPath()` 函数，使用系统级路径规范化
- **主要方法**: 优先使用 `fs.realpathSync()` 进行真正的路径规范化
- **系统级处理**:
  - 解析符号链接和 junction points
  - 自动规范化路径分隔符（Windows 上使用反斜杠）
  - 解析相对路径组件（`.` 和 `..`）
  - 返回规范的绝对路径
- **回退机制**: 如果 `fs.realpathSync()` 失败，回退到字符串处理：
  - 统一使用反斜杠分隔符
  - 清理重复的反斜杠（保留 UNC 路径的双反斜杠）
  - 移除末尾的反斜杠（根目录除外）

#### 2. UNC 路径支持
- **功能**: 正确处理网络路径格式 `\\server\share`
- **长路径处理**: UNC 路径转换为 `\\?\UNC\server\share` 格式

#### 3. 长路径支持
- **问题**: Windows 传统路径长度限制为 260 字符
- **解决方案**: 自动为超过 260 字符的路径添加 `\\?\` 前缀
- **示例**: `C:\very\long\path...` → `\\?\C:\very\long\path...`

#### 4. 路径验证
- **功能**: 添加 `validateWindowsPath()` 函数
- **检查项目**:
  - 检查非法字符：`< > : " | ? * \x00-\x1f`
  - 验证 Windows 路径格式（驱动器号或 UNC 路径）
  - 检查路径长度限制（32767 字符）
  - 检查保留名称：`CON`, `PRN`, `AUX`, `NUL`, `COM1-9`, `LPT1-9`

#### 5. CF_HDROP 格式支持
- **功能**: 添加 `createCFHDROPBuffer()` 函数创建拖放格式数据
- **结构**: DROPFILES 头部 (20 字节) + UTF-16 路径 + 双 null 终止符
- **用途**: 支持拖放操作，提高与 Windows 应用的兼容性

#### 6. 多格式支持
- **主格式**: `FileNameW` (UTF-16 编码)
- **备选格式1**: `CF_HDROP` (拖放格式)
- **备选格式2**: `FileName` (ANSI 编码，兼容旧应用)
- **备选格式3**: 纯文本格式

## 错误处理改进

### 1. 格式验证机制
- **重要发现**: `clipboard.writeBuffer` 使用不支持的格式时不会抛出错误
- **关键洞察**: 能读回数据 ≠ 系统真正支持该格式
- **正确解决方案**: 实现 `verifyClipboardFormat()` 函数，使用 `clipboard.availableFormats()` 检查
- 写入后检查 `availableFormats()` 返回的数组中是否包含该格式
- 只有在 `availableFormats()` 中出现的格式才是系统真正支持的

### 2. 可靠的回退机制
- 不再依赖 try-catch 捕获格式错误（因为不会抛出）
- 使用验证函数确保每种格式都被正确测试
- 按优先级顺序尝试格式，确保至少有一种格式成功

### 3. 详细的日志记录
- 成功时记录使用的格式
- 格式不支持时记录警告信息
- 便于调试和问题排查

## 兼容性提升

### 1. 向后兼容
- 保持原有的函数签名和调用方式
- 不影响现有代码的使用

### 2. 跨应用兼容
- 支持多种剪切板格式，提高与不同应用的兼容性
- 特别是 Windows 平台的 CF_HDROP 格式，支持拖放操作

### 3. 边缘情况处理
- 处理特殊字符、长路径、网络路径等边缘情况
- 提供合理的回退机制

## 测试建议

1. **基本功能测试**: 复制普通文件路径
2. **特殊字符测试**: 复制包含特殊字符的文件路径
3. **长路径测试**: 复制超长路径（Windows）
4. **网络路径测试**: 复制 UNC 路径（Windows）
5. **错误处理测试**: 测试无效路径的处理
6. **跨应用测试**: 在不同应用中粘贴，验证兼容性

## 性能影响

- 路径验证和规范化会增加少量计算开销
- 多格式尝试可能增加执行时间，但提高了成功率
- 整体性能影响很小，用户体验显著提升

## 未来改进建议

1. 添加更多的剪切板格式支持
2. 考虑添加配置选项，允许用户选择优先格式
3. 添加更详细的错误分类和处理
4. 考虑添加异步路径验证，避免阻塞主线程
