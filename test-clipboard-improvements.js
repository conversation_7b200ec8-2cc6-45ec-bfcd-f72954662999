#!/usr/bin/env node

/**
 * 测试改进后的剪切板功能
 * 这个脚本用于验证 copyFilePathMacOS 和 copyFilePathWindows 函数的改进
 */

import { copyFileToClipboard } from './electron/helpers/clipboard.js'
import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 测试用例
const testCases = [
  {
    name: '普通文件路径',
    path: path.join(__dirname, 'package.json'),
    shouldExist: true
  },
  {
    name: '包含特殊字符的路径',
    path: path.join(__dirname, 'test & file <with> "special" \'chars\'.txt'),
    shouldExist: false // 这个文件不存在，但我们可以测试路径验证
  },
  {
    name: '长路径测试',
    path: path.join(__dirname, 'very'.repeat(50), 'long', 'path', 'test.txt'),
    shouldExist: false
  }
]

// 创建测试文件
async function createTestFiles() {
  console.log('创建测试文件...')
  
  // 创建包含特殊字符的测试文件
  const specialCharFile = path.join(__dirname, 'test_special_chars.txt')
  try {
    fs.writeFileSync(specialCharFile, 'Test file with special characters in path')
    console.log(`✓ 创建测试文件: ${specialCharFile}`)
    return specialCharFile
  } catch (error) {
    console.warn(`⚠ 无法创建特殊字符测试文件: ${error.message}`)
    return null
  }
}

// 清理测试文件
function cleanupTestFiles(filePath) {
  if (filePath && fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath)
      console.log(`✓ 清理测试文件: ${filePath}`)
    } catch (error) {
      console.warn(`⚠ 无法清理测试文件: ${error.message}`)
    }
  }
}

// 运行测试
async function runTests() {
  console.log('开始测试改进后的剪切板功能...\n')
  
  const testFile = await createTestFiles()
  
  // 测试现有文件
  const existingFile = path.join(__dirname, 'package.json')
  if (fs.existsSync(existingFile)) {
    console.log(`测试 1: 复制现有文件路径`)
    console.log(`文件路径: ${existingFile}`)
    
    try {
      const result = await copyFileToClipboard(existingFile)
      console.log(`结果: ${result ? '✓ 成功' : '✗ 失败'}`)
    } catch (error) {
      console.error(`✗ 错误: ${error.message}`)
    }
    console.log('')
  }
  
  // 测试特殊字符文件
  if (testFile) {
    console.log(`测试 2: 复制包含特殊字符的文件路径`)
    console.log(`文件路径: ${testFile}`)
    
    try {
      const result = await copyFileToClipboard(testFile)
      console.log(`结果: ${result ? '✓ 成功' : '✗ 失败'}`)
    } catch (error) {
      console.error(`✗ 错误: ${error.message}`)
    }
    console.log('')
  }
  
  // 测试不存在的文件
  console.log(`测试 3: 复制不存在的文件路径`)
  const nonExistentFile = path.join(__dirname, 'non_existent_file.txt')
  console.log(`文件路径: ${nonExistentFile}`)
  
  try {
    const result = await copyFileToClipboard(nonExistentFile)
    console.log(`结果: ${result ? '✓ 成功' : '✗ 失败（预期）'}`)
  } catch (error) {
    console.log(`✓ 正确处理了不存在的文件: ${error.message}`)
  }
  console.log('')
  
  // 测试无效路径
  console.log(`测试 4: 复制无效路径`)
  const invalidPath = process.platform === 'win32' ? 'C:\\invalid<>path' : '/invalid\0path'
  console.log(`文件路径: ${invalidPath}`)
  
  try {
    const result = await copyFileToClipboard(invalidPath)
    console.log(`结果: ${result ? '✓ 成功' : '✗ 失败（预期）'}`)
  } catch (error) {
    console.log(`✓ 正确处理了无效路径: ${error.message}`)
  }
  console.log('')
  
  // 清理
  cleanupTestFiles(testFile)
  
  console.log('测试完成！')
  console.log('\n改进总结:')
  console.log('✓ 添加了 XML 转义处理（macOS）')
  console.log('✓ 添加了路径验证和规范化')
  console.log('✓ 支持多种剪切板格式作为备选')
  console.log('✓ 改进了错误处理和日志记录')
  console.log('✓ 支持长路径处理（Windows）')
  console.log('✓ 支持 UNC 路径（Windows）')
  console.log('✓ 添加了保留名称检查（Windows）')
}

// 检查是否在 Electron 环境中运行
if (typeof window !== 'undefined' && window.electron) {
  runTests().catch(console.error)
} else {
  console.log('此测试需要在 Electron 环境中运行')
  console.log('请在应用中调用这些函数进行测试')
  
  // 显示改进的功能列表
  console.log('\n改进后的功能:')
  console.log('\n🔧 关键修复:')
  console.log('- clipboard.writeBuffer 使用不支持的格式时不会抛错')
  console.log('- 能读回数据 ≠ 系统真正支持该格式')
  console.log('- 实现了 verifyClipboardFormat() 函数使用 clipboard.availableFormats() 验证')
  console.log('- 不再依赖 try-catch 进行格式回退，使用 availableFormats() 检查')

  console.log('\nmacOS (copyFilePathMacOS):')
  console.log('- XML 转义处理，防止路径中的特殊字符破坏 plist 格式')
  console.log('- 路径验证，检查路径格式和长度限制')
  console.log('- 多格式支持：NSFilenamesPboardType、public.file-url、纯文本')
  console.log('- 符号链接解析和路径规范化')
  console.log('- 可靠的格式验证和回退机制')

  console.log('\nWindows (copyFilePathWindows):')
  console.log('- 系统级路径规范化，优先使用 fs.realpathSync()')
  console.log('- 自动解析符号链接和 junction points')
  console.log('- 处理相对路径组件（. 和 ..）')
  console.log('- UNC 路径支持（\\\\server\\share 格式）')
  console.log('- 长路径支持（超过 260 字符，自动添加 \\\\?\\ 前缀）')
  console.log('- 路径验证，检查非法字符和保留名称')
  console.log('- 多格式支持：FileNameW、CF_HDROP、FileName、纯文本')
  console.log('- 可靠的格式验证和回退机制')
}
