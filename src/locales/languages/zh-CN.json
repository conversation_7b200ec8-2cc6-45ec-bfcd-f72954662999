{"common.empty": "暂无数据", "common.yes": "是", "common.no": "否", "common.cancel": "取消", "common.confirm": "确认", "common.restart": "重启", "common.default": "默认", "common.tips": "提示", "common.open": "打开", "common.input.placeholder": "请填写", "common.success": "操作成功", "common.success.batch": "批量操作成功", "common.fail": "操作失败", "common.starting": "启动中", "common.loading": "加载中", "common.search": "搜索", "common.batch": "批量", "common.device": "设备", "common.progress": "进行中", "common.finished": "已结束", "common.stop": "终止", "common.remove": "移除", "common.select.please": "请选择", "common.required": "该选项不能为空", "common.download": "下载", "common.downloading": "正在下载中", "common.delete": "删除", "common.name": "名称", "common.size": "大小", "common.warning": "警告", "common.info": "消息", "common.danger": "错误", "common.connecting": "连接中", "common.language.name": "语言", "common.language.placeholder": "选择你需要的语言", "time.update": "更新时间", "time.unit.month": "月", "time.unit.week": "周", "time.unit.day": "天", "time.unit.hour": "小时", "time.unit.minute": "分钟", "time.unit.second": "秒", "time.unit.millisecond": "毫秒", "appClose.name": "关闭主面板", "appClose.question": "每次询问", "appClose.minimize": "最小化到托盘", "appClose.quit": "直接退出", "appClose.quit.cancel": "取消退出", "appClose.quit.loading": "正在停止服务...", "appClose.message": "确定要退出吗？", "appClose.remember": "是否记住选择？", "dependencies.lack.title": "注意事项", "dependencies.lack.content": "此软件依赖于{name}。请确保正确安装了上述依赖项，或手动配置偏好设置中依赖项的位置", "device.list": "设备列表", "device.list.empty": "没有检测到设备", "device.serial": "设备标识", "device.name": "设备名称", "device.remark": "备注", "device.permission.error": "设备可能未授权成功，请重新插拔设备并点击允许USB调试", "device.terminal.name": "终端调试", "device.status": "状态", "device.status.offline": "已离线", "device.status.unauthorized": "未授权", "device.status.connected": "已连接", "device.status.authorizing": "授权中", "device.battery": "设备电量", "device.isCharging": "充电状态", "device.temperature": "设备温度", "device.powerSource": "驱动来源", "device.voltage": "设备电压", "device.config.migration.title": "配置迁移", "device.config.migration.description.title": "检测到该设备存在配置", "device.config.migration.description.content": "是否将该配置迁移到当前设备列表中同一设备的条目？这将帮助您保留之前的设置。", "device.config.migration.select.title": "请选择要迁移配置的目标设备：", "device.config.migration.no.target.devices": "未找到可迁移的目标设备", "device.config.migration.skip": "跳过", "device.config.migration.start": "开始迁移", "device.config.migration.success": "成功迁移 {count} 个设备的配置", "device.config.migration.no.migration": "没有配置需要迁移", "device.config.migration.error": "配置迁移失败", "device.task.name": "计划任务", "device.task.tips": " 注意：请确保你的计算机保持唤醒状态，否则计划任务将无法被正常执行。", "device.task.list": "任务列表", "device.task.type": "任务类型", "device.task.frequency": "执行频率", "device.task.frequency.timeout": "单次执行", "device.task.frequency.interval": "周期重复", "device.task.timeout": "执行时间", "device.task.timeout.tips": "不能小于当前时间", "device.task.timeout.expired": "该任务已过期", "device.task.interval": "重复间隔", "device.task.devices": "涉及设备", "device.task.noRepeat": "不重复", "device.task.restart": "再次执行", "device.task.extra.app": "选择应用", "device.task.extra.shell": "选择脚本", "device.wireless.name": "无线", "device.wireless.mode": "无线模式", "device.wireless.mode.error": "没有获取到局域网连接地址，请检查网络", "device.wireless.connect.qr": "二维码连接", "device.wireless.connect.qr.pairing": "配对中", "device.wireless.connect.qr.connecting": "连接中", "device.wireless.connect.qr.connecting-fallback": "连接中", "device.wireless.connect.qr.connected": "连接成功", "device.wireless.connect.qr.error": "二维码连接", "device.wireless.connect.name": "连接设备", "device.wireless.connect.error.title": "连接设备失败", "device.wireless.connect.error.detail": "错误详情", "device.wireless.connect.error.reasons[0]": "可能有以下原因", "device.wireless.connect.error.reasons[1]": "IP地址或端口号错误", "device.wireless.connect.error.reasons[2]": "设备未与当前电脑配对成功", "device.wireless.connect.error.reasons[3]": "电脑网络和提供的设备网络IP不在同一个局域网中", "device.wireless.connect.error.reasons[4]": "adb 依赖路径错误", "device.wireless.connect.error.reasons[5]": "其他未知错误", "device.wireless.connect.error.confirm": "无线配对", "device.wireless.connect.error.cancel": "@:common.cancel", "device.wireless.connect.error.no-address": "无线调试地址不能为空", "device.wireless.connect.success": "连接设备成功", "device.wireless.connect.batch.name": "连接所有历史设备", "device.wireless.disconnect.start": "断开连接", "device.wireless.disconnect.progress": "正在断开", "device.wireless.disconnect.success": "断开连接成功", "device.wireless.pair": "无线配对", "device.wireless.pair.tips": "注意：可以在 开发者选项 -> 无线调试(可以点进去) -> 使用配对码配对设备中获取以下信息", "device.wireless.pair.address": "配对IP地址", "device.wireless.pair.address.message": "配对码不能为空", "device.wireless.pair.address.placeholder": "请输入配对IP地址", "device.wireless.pair.port": "配对端口号", "device.wireless.pair.port.message": "配对端口号不能为空", "device.wireless.pair.port.placeholder": "请输入配对端口号", "device.wireless.pair.code": "配对码", "device.wireless.pair.code.message": "配对码不能为空", "device.wireless.pair.code.placeholder": "请输入配对码", "device.refresh.name": "刷新设备", "device.restart.name": "重启服务", "device.log.name": "运行日志", "device.arrange.name": "窗口编排", "device.arrange.dialog.title": "窗口编排", "device.arrange.widget.add": "添加组件", "device.arrange.widget.global": "全局配置", "device.arrange.layout.reset": "重置布局", "device.arrange.layout.clear": "清除全部", "device.arrange.layout.save": "保存布局", "device.arrange.widget.size": "尺寸", "device.arrange.widget.position": "位置", "device.arrange.save.noChanges": "没有更改需要保存", "device.arrange.save.success": "已成功保存 {count} 个组件的布局{removed}", "device.arrange.save.removed": "，移除了 {count} 个", "device.arrange.widget.global.exists": "全局组件已存在", "device.arrange.widget.global.name": "全局", "device.arrange.device.notFound": "设备未找到", "device.arrange.clear.confirm": "确定要清除所有组件吗？", "device.arrange.clear.title": "确认", "device.arrange.clear.success": "已清除所有组件", "device.mirror.start": "开始镜像", "device.record.progress": "正在录制", "device.record.success.title": "录屏成功，并已复制到剪切板", "device.record.success.message": "是否前往录制位置进行查看？", "device.actions.more.name": "更多操作", "device.actions.more.record.name": "开始录制", "device.actions.more.camera.name": "启动相机", "device.actions.more.recordCamera.name": "录制相机", "device.actions.more.recordAudio.name": "录制音频", "device.actions.more.otg.name": "启动OTG", "device.actions.more.custom.name": "灵活启动", "device.control.name": "操作", "device.control.more": "设备交互", "device.control.install": "安装APP", "device.control.install.placeholder": "请选择要安装的应用", "device.control.install.progress": "正在为 {<PERSON><PERSON><PERSON>} 安装应用中...", "device.control.install.success": "已成功将应用安装到 {deviceName} 中，共 {totalCount}个，成功 {successCount}个，失败 {failCount}个", "device.control.install.success.single": "已成功将应用安装到 {deviceName} 中", "device.control.install.error": "安装应用失败，请检查安装包后重试", "device.control.file.name": "文件管理", "device.control.file.push": "推送文件", "device.control.file.push.placeholder": "请选择要推送的文件", "device.control.file.push.loading": "推送文件中", "device.control.file.push.success.name": "推送文件成功", "device.control.file.push.success": "已成功将 {totalCount} 个文件推送到 {deviceName}，{successCount} 成功，{failCount} 失败。", "device.control.file.push.success.single": "文件已成功推送到 {deviceName}", "device.control.file.push.error": "推送文件失败，请检查文件后重试", "device.control.file.manager.storage": "内部存储空间", "device.control.file.manager.add": "新建文件夹", "device.control.file.manager.upload": "上传文件", "device.control.file.manager.upload.directory": "上传目录", "device.control.file.manager.download": "下载文件", "device.control.file.manager.download.tips": "确定要下载所选内容吗", "device.control.file.manager.delete.tips": "确定要删除所选内容吗", "device.control.terminal.command.name": "执行命令", "device.control.terminal.script.name": "执行脚本", "device.control.terminal.script.select": "请选择要执行的脚本", "device.control.terminal.script.push.loading": "推送脚本中...", "device.control.terminal.script.push.success": "推送脚本成功", "device.control.terminal.script.enter": "请输入回车键确认执行该脚本", "device.control.terminal.script.success": "脚本执行成功", "device.control.capture": "截取屏幕", "device.control.capture.progress": "正在截取 {deviceName} 的屏幕快照...", "device.control.capture.success.message": "是否前往截屏位置进行查看？", "device.control.capture.success.message.title": "截图成功，并已复制到剪切板", "device.control.reboot": "重启设备", "device.control.turnScreenOff": "关闭屏幕", "device.control.turnScreenOff.tips": "关闭屏幕且保持控制（实验功能）：此操作将创建一个 EscrcpyHelper 进程，手动关闭进程将重新打开屏幕。", "device.control.startApp": "启动APP", "device.control.startApp.useMainScreen": "使用主显示器打开", "device.control.power": "电源键", "device.control.power.tips": "可以用来开启或关闭屏幕", "device.control.notification": "通知栏", "device.control.notification.tips": "打开下拉菜单选项", "device.control.return": "返回键", "device.control.home": "主屏幕", "device.control.switch": "切换键", "device.control.gnirehtet": "反向供网", "device.control.gnirehtet.tips": "使用 Gnirehtet 为 Android 提供反向网络共享；注意：首次连接需要在设备上进行授权", "device.control.gnirehtet.start": "启动服务", "device.control.gnirehtet.start.success": "Gnirehtet 反向网络共享功能启动成功", "device.control.gnirehtet.stop": "停止服务", "device.control.gnirehtet.stop.success": "停止服务成功", "device.control.gnirehtet.running": "服务运行中", "device.control.gnirehtet.stopping": "服务停止中", "device.control.mirror-group.name": "多屏协同", "device.control.mirror-group.tips": "开启后，可以同时镜像多个模拟辅助显示设备，并通过操作各个镜像窗口实现多屏协同功能。请注意，此功能需要手机 ROM 支持，并且必须开启强制使用桌面模式选项。", "device.control.mirror-group.open": "开启 {num} 个窗口", "device.control.mirror-group.close": "关闭辅助显示设备", "device.control.mirror-group.appClose.tips": "用于解决某些机型退出所有控制窗口后自动关闭失败的问题", "device.control.volume.name": "音量控制", "device.control.volume-up.name": "增加音量", "device.control.volume-down.name": "减小音量", "device.control.volume-mute.name": "关闭音量", "device.control.rotation.name": "旋转屏幕", "device.control.rotation.vertically": "纵向旋转", "device.control.rotation.horizontally": "横向旋转", "device.control.rotation.auto": "自动旋转", "device.control.rotation.disable": "禁用旋转", "preferences.name": "偏好设置", "preferences.reset": "恢复默认值", "preferences.scope.global": "全局", "preferences.scope.placeholder": "偏好设置的作用域范围", "preferences.scope.details[0]": "对全局或者单个设备设置不同的偏好配置", "preferences.scope.details[1]": "全局:将对所有设备生效。", "preferences.scope.details[2]": "单个设备:继承于全局配置,并对单个设备进行独立设置,仅对此设备生效。", "preferences.config.import.name": "导入", "preferences.config.import.placeholder": "请选择要导入的配置文件", "preferences.config.import.success": "导入成功", "preferences.config.export.name": "导出", "preferences.config.export.message": "导出配置", "preferences.config.export.placeholder": "请选择要导出的位置", "preferences.config.export.success": "导出成功", "preferences.config.edit.name": "编辑", "preferences.config.reset.name": "重置", "preferences.config.reset.tips": "注意：重置后，之前保存的配置将会被清除，因此建议在执行重置操作之前备份您的配置。", "preferences.common.name": "通用设置", "preferences.common.theme.name": "主题", "preferences.common.theme.placeholder": "设置主题", "preferences.common.theme.options[0]": "浅色模式", "preferences.common.theme.options[1]": "深色模式", "preferences.common.theme.options[2]": "跟随系统", "preferences.common.debug.name": "调试", "preferences.common.debug.placeholder": "是否启动软件调试", "preferences.common.debug.tips": "启用后可以在运行日志中查看软件运行情况，一般不需要开启可能会影响性能，注意: 改变此选项需要重启软件后才能生效。", "preferences.common.file.name": "文件存储路径", "preferences.common.file.placeholder": "用户桌面", "preferences.common.file.tips": "截图和录制的音视频存放在这里", "preferences.common.adb.name": "adb 路径", "preferences.common.adb.placeholder": "自定义 adb 路径", "preferences.common.adb.tips": "用于连接设备的 adb 地址。", "preferences.common.scrcpy.name": "scrcpy 路径", "preferences.common.scrcpy.placeholder": "自定义 scrcpy 路径", "preferences.common.scrcpy.tips": "用于控制设备的 scrcpy 地址。", "preferences.common.scrcpy.append.name": "scrcpy 参数", "preferences.common.scrcpy.append.placeholder": "为 scrcpy 命令追加额外的参数", "preferences.common.scrcpy.append.tips": "注意：录入参数将会直接附加到 scrcpy 命令中，如果存在重复的参数，并不会自动进行过滤。", "preferences.common.gnirehtet.name": "gnirehtet 路径", "preferences.common.gnirehtet.placeholder": "自定义 gnirehtet 路径", "preferences.common.gnirehtet.tips": "用于为设备反向供网的 gnirehtet 地址。", "preferences.common.gnirehtet.fix.name": "gnirehtet 修复", "preferences.common.gnirehtet.fix.placeholder": "开启后，将禁用 gnirehtet.apk 的安装检查，这可能改善某些设备的连接问题", "preferences.common.gnirehtet.fix.tips": "注意：这可能会导致每次启动时重新安装 gnirehtet.apk。", "preferences.common.gnirehtet.append.name": "gnirehtet 参数", "preferences.common.gnirehtet.append.placeholder": "为 gnirehtet 命令追加额外的参数", "preferences.common.gnirehtet.append.tips": "注意：录入参数将会直接附加到 gnirehtet 命令中，如果存在重复的参数，并不会自动进行过滤。", "preferences.common.floatControl.name": "浮动操控栏", "preferences.common.floatControl.placeholder": "启用后，镜像时将会自动打开设备浮动操控栏", "preferences.common.auto-connect.name": "自动连接设备", "preferences.common.auto-connect.placeholder": "启用后，该软件将在启动时尝试自动连接到历史无线设备", "preferences.common.auto-mirror.name": "自动执行镜像", "preferences.common.auto-mirror.placeholder": "启用后，设备列表中的设备将自动运行镜像", "preferences.common.edgeHidden.name": "主面板贴边隐藏", "preferences.common.edgeHidden.placeholder": "启用后，当鼠标靠近屏幕边缘离开面板时，主面板将自动隐藏。", "preferences.common.edgeHidden.tips": "注意：更改此选项后，需要重启应用才能生效", "preferences.common.imeFix.name": "启动APP键盘修复", "preferences.common.imeFix.placeholder": "启用后，将解决APP启动时输入法无法在当前镜像窗口显示的问题。", "preferences.common.imeFix.tips": "注意：此功能仅支持 scrcpy v3.2 及以上版本，低版本使用时会报错。", "preferences.video.name": "视频控制", "preferences.video.disable-video.name": "禁用视频转发", "preferences.video.disable-video.placeholder": "开启后将禁用视频转发", "preferences.video.video-source.name": "视频源", "preferences.video.video-source.placeholder": "默认为设备显示器", "preferences.video.video-source.display": "显示器", "preferences.video.video-source.camera": "摄像头", "preferences.video.resolution.name": "最大尺寸", "preferences.video.resolution.placeholder": "设备尺寸，格式：1080", "preferences.video.bit.name": "视频比特率", "preferences.video.bit.placeholder": "8000000，格式：8M，8000000", "preferences.video.refresh-rate.name": "刷新率", "preferences.video.refresh-rate.placeholder": "60", "preferences.video.video-code.name": "视频编码", "preferences.video.video-code.placeholder": "h.264", "preferences.video.display-orientation.name": "显示方向", "preferences.video.display-orientation.placeholder": "设备显示方向", "preferences.video.angle.name": "旋转角度", "preferences.video.angle.placeholder": "不旋转，格式：15", "preferences.video.angle.tips": "注意：该选项在录制时同样有效", "preferences.video.screen-cropping.name": "屏幕裁剪", "preferences.video.screen-cropping.placeholder": "不裁剪，格式：1224:1440:0:0", "preferences.video.display.name": "显示器", "preferences.video.display.placeholder": "设备显示器", "preferences.video.video-buffer.name": "视频缓冲", "preferences.video.video-buffer.placeholder": "0", "preferences.video.receiver-buffer.name": "接收器缓冲(v412)", "preferences.video.receiver-buffer.placeholder": "0", "preferences.device.name": "设备控制", "preferences.device.show-touch.name": "展示触摸点", "preferences.device.show-touch.placeholder": "开启后将激活开发者选项中的显示点按触摸反馈", "preferences.device.show-touch.tips": "仅在物理设备上展示", "preferences.device.stay-awake.name": "保持唤醒", "preferences.device.stay-awake.placeholder": "开启后将防止设备进入睡眠状态", "preferences.device.stay-awake.tips": "仅有线方式连接时有效", "preferences.device.turnScreenOff.name": "控制时关闭屏幕", "preferences.device.turnScreenOff.placeholder": "开启后控制设备时将自动关闭设备屏幕", "preferences.device.screenOffTimeout.name": "屏幕超时", "preferences.device.screenOffTimeout.placeholder": "设备默认", "preferences.device.screenOffTimeout.tips": "修改屏幕关闭超时设置，并在退出时恢复设备默认", "preferences.device.control-end-video.name": "控制结束关闭屏幕", "preferences.device.control-end-video.placeholder": "开启后停止控制设备将自动关闭设备屏幕", "preferences.device.control-in-stop-charging.name": "控制时禁用自动亮屏", "preferences.device.control-in-stop-charging.placeholder": "开启后控制设备时将禁用自动亮屏", "preferences.device.control-in-stop-charging.tips": "开启后控制设备时将禁用自动亮屏", "preferences.device.display-overlay.name": "模拟辅助显示器", "preferences.device.display-overlay.placeholder": "设备大小，格式：1920x1080/420，1920x1080，/240", "preferences.device.display-overlay.tips": "用于调整模拟辅助显示器的大小和分辨率，启动应用、多屏协同（镜像组）依赖于此选项", "preferences.window.name": "窗口控制", "preferences.window.borderless.name": "无边框模式", "preferences.window.borderless.placeholder": "开启后控制窗口将变为无边框模式", "preferences.window.full-screen.name": "全屏模式", "preferences.window.full-screen.placeholder": "开启后控制窗口将全屏显示模式", "preferences.window.always-top.name": "始终位于顶部", "preferences.window.always-top.placeholder": "开启后控制窗口将始终位于顶部", "preferences.window.disable-screen-saver.name": "禁用屏幕保护程序", "preferences.window.disable-screen-saver.placeholder": "开启后将禁用计算机屏幕保护程序", "preferences.window.size.width": "窗口宽度", "preferences.window.size.width.placeholder": "设备宽度", "preferences.window.size.width.tips": "注意：更改此设置可能会导致显示模糊", "preferences.window.size.height": "窗口高度", "preferences.window.size.height.placeholder": "设备高度", "preferences.window.size.height.tips": "注意：更改此设置可能会导致显示模糊", "preferences.window.position.x": "窗口横坐标", "preferences.window.position.x.placeholder": "相对于桌面中心", "preferences.window.position.y": "窗口纵坐标", "preferences.window.position.y.placeholder": "相对于桌面中心", "preferences.record.name": "音视频录制", "preferences.record.format.name": "视频格式", "preferences.record.format.placeholder": "mp4", "preferences.record.format.audio.name": "音频格式", "preferences.record.format.audio.placeholder": "opus", "preferences.record.time-limit.name": "录制时长", "preferences.record.time-limit.placeholder": "不限时长", "preferences.record.orientation.name": "录制视频方向", "preferences.record.orientation.placeholder": "设备默认方向", "preferences.record.no-video-playback.name": "禁用视频播放", "preferences.record.no-video-playback.placeholder": "开启后录制时将禁用视频播放", "preferences.record.no-video-playback.tips": "注意：只是禁用了播放但是依然会录制视频", "preferences.record.no-audio-playback.name": "禁用音频播放", "preferences.record.no-audio-playback.placeholder": "开启后录制时将禁用音频播放", "preferences.record.no-audio-playback.tips": "注意：只是禁用了播放但是依然会录制音频", "preferences.audio.name": "音频控制", "preferences.audio.disable-audio.name": "禁用音频转发", "preferences.audio.disable-audio.placeholder": "开启后将禁用音频转发", "preferences.audio.disable-audio.tips": "如果您的设备音频捕获异常，则可以打开此选项，以确保可以正常打开镜像", "preferences.audio.audioDup.name": "保持设备音频", "preferences.audio.audioDup.placeholder": "开启后镜像时将保持音频在设备上播放", "preferences.audio.audioDup.tips": "注意：该选项需要 Android 13+，并且应用程序可以选择退出（这种情况下它们将不会被捕获）", "preferences.audio.audio-source.name": "音频源", "preferences.audio.audio-source.placeholder": "设备音频输出", "preferences.audio.audio-source.tips": "技巧：如果将来源设为麦克风将可以在录制时将声音录制下来", "preferences.audio.audio-source.output": "设备输出", "preferences.audio.audio-source.mic": "麦克风", "preferences.audio.audio-source.playback": "捕获音频播放（Android 应用程序可以选择退出，因此不一定捕获整个输出）", "preferences.audio.audio-source.mic-unprocessed": "捕获麦克风未处理的（原始）声音", "preferences.audio.audio-source.mic-camcorder": "捕获针对视频录制而调谐的麦克风，如果可用，其方向与摄像头相同", "preferences.audio.audio-source.mic-voice-recognition": "捕获针对语音识别进行调节的麦克风", "preferences.audio.audio-source.mic-voice-communication": "捕获针对语音通信进行调谐的麦克风（例如，如果可用，它将利用回声消除或自动增益控制）", "preferences.audio.audio-source.voice-call": "捕获语音呼叫", "preferences.audio.audio-source.voice-call-uplink": "仅捕获语音呼叫上行链路", "preferences.audio.audio-source.voice-call-downlink": "仅捕获语音呼叫下行链路", "preferences.audio.audio-source.voice-performance": "捕获用于现场表演（卡拉 OK）的音频，包括麦克风和设备播放", "preferences.audio.audio-code.name": "音频编码", "preferences.audio.audio-code.placeholder": "opus", "preferences.audio.audio-bit-rate.name": "音频比特率", "preferences.audio.audio-bit-rate.placeholder": "128000，格式：128K，128000", "preferences.audio.audio-bit-rate.tips": "注意：此选项不适用于 RAW 音频编解码器", "preferences.audio.audio-buffer.name": "音频缓冲", "preferences.audio.audio-buffer.placeholder": "0", "preferences.audio.audio-output-buffer.name": "音频输出缓冲", "preferences.audio.audio-output-buffer.placeholder": "5", "preferences.input.name": "输入控制", "preferences.input.mouse.name": "鼠标模式", "preferences.input.mouse.tips": "设置鼠标输入模式", "preferences.input.mouse.placeholder": "sdk", "preferences.input.mouse.options[0].placeholder": "默认", "preferences.input.mouse.options[1].placeholder": "使用设备上的 UHID 内核模块模拟物理 HID 鼠标", "preferences.input.mouse.options[2].placeholder": "使用 AOAv2 协议模拟物理 HID 鼠标", "preferences.input.mouse.options[3].placeholder": "禁用鼠标输入", "preferences.input.mouse-bind.name": "鼠标绑定", "preferences.input.mouse-bind.tips": "该选项允许自定义鼠标按键功能。它使用两组4字符序列来定义主要和次要(Shift键)绑定。每个字符代表一个鼠标按键(右键、中键、第4键、第5键),可以设置为:'+' 转发到设备, '-' 忽略, 'b' 返回, 'h' 主页, 's' 切换应用, 'n' 展开通知面板。例如, --mouse-bind=bhsn:++++ 表示主要绑定为 返回/主页/切换应用/通知,次要绑定全部转发到设备。", "preferences.input.mouse-bind.placeholder": "bhsn:++++", "preferences.input.keyboard.name": "键盘模式", "preferences.input.keyboard.tips": "设置键盘输入模式", "preferences.input.keyboard.placeholder": "sdk", "preferences.input.keyboard.options[0].placeholder": "默认", "preferences.input.keyboard.options[1].placeholder": "使用设备上的 UHID 内核模块模拟物理 HID 键盘", "preferences.input.keyboard.options[2].placeholder": "使用 AOAv2 协议模拟物理 HID 键盘", "preferences.input.keyboard.options[3].placeholder": "禁用键盘输入", "preferences.input.keyboard.inject.name": "键盘注入方式", "preferences.input.keyboard.inject.placeholder": "默认", "preferences.input.keyboard.inject.tips": "设置键盘文本注入首选项", "preferences.input.keyboard.inject.options[0].placeholder": "将字母作为文本注入", "preferences.input.keyboard.inject.options[1].placeholder": "强制始终注入原始按键事件", "preferences.input.gamepad.name": "游戏手柄", "preferences.input.gamepad.placeholder": "禁用", "preferences.input.gamepad.tips": "该选项将允许将游戏手柄（ PS4/PS5 或 XBox）连接到你的电脑来玩安卓游戏。注意：所玩的游戏必须支持游戏手柄输入。", "preferences.camera.name": "摄像控制", "preferences.camera.camera-facing.name": "摄像源", "preferences.camera.camera-facing.placeholder": "设备摄像源", "preferences.camera.camera-facing.front": "前置摄像", "preferences.camera.camera-facing.back": "后置摄像", "preferences.camera.camera-facing.external": "外接摄像", "preferences.camera.camera-size.name": "摄像尺寸", "preferences.camera.camera-size.placeholder": "设备摄像尺寸，格式：1920x1080", "preferences.camera.camera-ar.name": "摄像比例", "preferences.camera.camera-ar.placeholder": "设备摄像比例，格式：4:3，sensor，1.6", "preferences.camera.camera-fps.name": "摄像帧率", "preferences.camera.camera-fps.placeholder": "设备摄像帧率", "about.name": "关于", "about.description": "📱 使用图形化的 Scrcpy 显示和控制您的 Android 设备, 由 Electron 驱动", "about.update": "检查并更新", "about.update-not-available": "已经是最新版本", "about.update-error.title": "检查更新失败", "about.update-error.message": "你可能需要科学上网,是否前往发布页面手动下载更新?", "about.update-downloaded.title": "下载新版本成功", "about.update-downloaded.message": "是否立即重启更新?", "about.update-downloaded.confirm": "更新", "about.update-available.title": "发现新版本", "about.update-available.confirm": "更新", "about.update.progress": "正在更新中", "about.donate.title": "捐赠", "about.donate.description": "如果该项目帮到你的话，可以请我喝杯咖啡，让我更有精神完善该项目 😛", "about.docs.name": "帮助文档", "desktop.shortcut.add": "添加到桌面快捷方式"}